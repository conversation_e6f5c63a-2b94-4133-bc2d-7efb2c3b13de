use std::ops::Deref;

use deadpool_postgres::PoolError;
use native_tls::TlsConnector;
use postgres_native_tls::MakeTlsConnector;
use util::{
    anyhow::{Context, Result},
    url::Url,
};

#[derive(Debug, <PERSON><PERSON>)]
pub struct PostgresPool {
    pool: deadpool_postgres::Pool,
}

pub struct PostgresPoolOptions {
    pub wait_timeout: std::time::Duration,
}

impl Default for PostgresPoolOptions {
    fn default() -> Self {
        Self {
            wait_timeout: std::time::Duration::from_secs(10),
        }
    }
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, De<PERSON>ult)]
pub struct PostgresPoolGetClientOptions {
    pub log_error: bool,
}

impl PostgresPool {
    pub fn new(url: &Url, max_size: usize) -> Result<Self> {
        PostgresPool::new_with_opts(url, max_size, PostgresPoolOptions::default())
    }

    pub fn new_with_opts(url: &Url, max_size: usize, options: PostgresPoolOptions) -> Result<Self> {
        let mut config = deadpool_postgres::Config::new();
        config.url = Some(url.to_string());
        config.manager = Some(deadpool_postgres::ManagerConfig {
            recycling_method: deadpool_postgres::RecyclingMethod::Fast,
        });
        // This effectively caps the number of concurrent locks to be max_locks (+ one
        // maintenance connection).
        config.pool = Some(deadpool_postgres::PoolConfig {
            max_size,
            timeouts: deadpool_postgres::Timeouts {
                wait: Some(options.wait_timeout),
                ..Default::default()
            },
            ..Default::default()
        });

        let tls = TlsConnector::builder()
            .danger_accept_invalid_certs(true)
            .build()
            .context("failed to build TLS connector")?;
        let connector = MakeTlsConnector::new(tls);

        let pool = config
            .create_pool(Some(deadpool_postgres::Runtime::Tokio1), connector)
            .context("failed to create postgres pool")?;

        Ok(Self { pool })
    }

    pub async fn get_client(&self) -> Result<deadpool_postgres::Object, PoolError> {
        self.get_client_with_opts(PostgresPoolGetClientOptions { log_error: true })
            .await
    }

    pub async fn get_client_with_opts(
        &self,
        opts: PostgresPoolGetClientOptions,
    ) -> Result<deadpool_postgres::Object, PoolError> {
        use crate::postgres_global_store::POOL_METRICS;
        use std::time::Instant;

        let start = Instant::now();
        let client = self.pool.get().await;
        let wait_time_ms = start.elapsed().as_millis() as u64;

        // Record wait time
        POOL_METRICS.connection_wait_time_ms.record(wait_time_ms, &[]);

        match client {
            Ok(client) => Ok(client),
            Err(e) => {
                // Check if it's a pool exhaustion error
                if matches!(e, PoolError::Timeout(_)) {
                    POOL_METRICS.pool_exhaustion_count.add(1, &[]);
                }

                if opts.log_error {
                    log::error!("Failed to get postgres client: {:?}", e);
                }
                Err(e)
            }
        }
    }
}

impl Deref for PostgresPool {
    type Target = deadpool_postgres::Pool;

    fn deref(&self) -> &Self::Target {
        &self.pool
    }
}
