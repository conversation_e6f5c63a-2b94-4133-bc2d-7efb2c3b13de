[package]
name = "pulse"
version = "0.1.0"
edition = "2024"

[dependencies]
actix-web = "4.9.0"
clap = { version = "4.5.18", features = ["derive", "env"] }
env_logger = "0.11.8"
log = "0.4.27"
serde = { version = "1.0.214", features = ["derive"] }
serde_json = "1.0.128"
reqwest = { version = "0.12.7", features = ["json"] }
tokio = { version = "1.40.0", features = ["process", "rt", "rt-multi-thread", "sync"] }
lru = "0.12.5"

# OpenTelemetry dependencies
opentelemetry = "0.30.0"
opentelemetry-proto = { version = "0.30.0", features = ["metrics"] }
prost = "0.13.5"

agent = { path = "../agent" }
util = { path = "../util" }
