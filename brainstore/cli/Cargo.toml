[package]
name = "brainstore"
version = "0.1.0"
edition = "2021"
authors = ["Braintrust engineering <<EMAIL>>"]
description = "The Brainstore command line interface"
build = "build.rs"

[dependencies]
btql = { path = "../btql" }
otel_common = { path = "../otel_common" }
agent = { path = "../agent" }
storage = { path = "../storage" }
util = { path = "../util" }
query = { path = "../query" }
tracing = { path = "../tracing" }

clap = { version = "4.5.18", features = ["derive", "env"] }
serde_json = "1.0.128"
tokio = { version = "1.40.0", features = ["process", "rt", "rt-multi-thread"] }
log = "0.4.22"
tokio-stream = "0.1.16"
num_cpus = "1.16.0"
serde = { version = "1.0.214", features = ["derive"] }
actix-web = "4.9.0"
async-stream = "0.3.6"
chrono = "0.4.38"
tantivy = { path = "../tantivy" }
fork = "0.2.0"
nix = { version = "0.29.0", features = ["process", "poll"] }
tempfile = "3.14.0"
rand = "0.8.5"
walkdir = "2.5.0"
reqwest = { version = "0.12.7", features = ["blocking", "json"] }
ring = { version = "0.17.8", optional = true }
base64 = { version = "0.21", optional = true }
backoff = { version = "0.4", optional = true }
deadpool = "0.12.1"
lazy_static = "1.5.0"

[target.'cfg(not(target_env = "msvc"))'.dependencies]
tikv-jemallocator = { version = "0.6.0", features = ["profiling", "unprefixed_malloc_on_supported_platforms"] }

# Continuous Profiling
tonic = { version = "0.12.3", features = ["tls", "tls-roots"], optional = true }
prost = "0.13.5"
prost-types = "0.13.5"



[build-dependencies]
tonic-build = { version = "0.12.3", features = ["prost"], optional = true }

[features]
default = ["expect-tests", "enable_memprof", "bench"]
distribute = ["ring", "base64", "backoff"]
expect-tests = ["query/expect-tests"]
enable_memprof = ["agent/enable_memprof"]
bench = []
