use opentelemetry::metrics::{Counter, Gauge, Meter};
use std::collections::HashMap;
use sysinfo::System;

use util::async_trait::async_trait;

use crate::{collector::Collector, process_info::get_memory_usage_with_children};

#[derive(Clone)]
#[allow(dead_code)]
struct DiskIoStats {
    read_bytes: u64,
    write_bytes: u64,
    read_count: u64,
    write_count: u64,
}

/// Comprehensive system metrics collector similar to Datadog's system metrics
#[allow(dead_code)]
pub struct SystemMetricsCollector {
    // CPU Metrics
    cpu_user: Gauge<f64>,
    cpu_system: Gauge<f64>,
    cpu_idle: Gauge<f64>,
    cpu_iowait: Gauge<f64>,
    cpu_steal: Gauge<f64>,
    cpu_interrupt: Gauge<f64>,
    cpu_guest: Gauge<f64>,
    cpu_nice: Gauge<f64>,
    cpu_softirq: Gauge<f64>,

    // CPU totals (cumulative)
    cpu_user_total: Counter<u64>,
    cpu_system_total: Counter<u64>,
    cpu_idle_total: Counter<u64>,
    cpu_iowait_total: Counter<u64>,
    cpu_steal_total: Counter<u64>,
    cpu_irq_total: Counter<u64>,
    cpu_softirq_total: Counter<u64>,
    cpu_nice_total: Counter<u64>,
    cpu_guest_total: Counter<u64>,
    cpu_guestnice_total: Counter<u64>,

    // CPU info
    cpu_num_cores: Gauge<u64>,
    cpu_context_switches: Counter<u64>,

    // Memory Metrics
    mem_total: Gauge<u64>,
    mem_available: Gauge<u64>,
    mem_used: Gauge<u64>,
    mem_free: Gauge<u64>,
    mem_cached: Gauge<u64>,
    mem_buffered: Gauge<u64>,
    mem_shared: Gauge<u64>,
    mem_slab: Gauge<u64>,
    mem_page_tables: Gauge<u64>,
    mem_commit_limit: Gauge<u64>,
    mem_committed_as: Gauge<u64>,
    mem_usable: Gauge<f64>,
    mem_pct_usable: Gauge<f64>,

    // Swap Metrics
    swap_total: Gauge<u64>,
    swap_used: Gauge<u64>,
    swap_free: Gauge<u64>,
    swap_pct_used: Gauge<f64>,
    swap_pct_free: Gauge<f64>,

    // Network Metrics
    net_bytes_sent: Counter<u64>,
    net_bytes_recv: Counter<u64>,
    net_packets_in_count: Counter<u64>,
    net_packets_out_count: Counter<u64>,
    net_packets_in_error: Counter<u64>,
    net_packets_out_error: Counter<u64>,

    // Disk Metrics
    disk_total: Gauge<u64>,
    disk_used: Gauge<u64>,
    disk_free: Gauge<u64>,
    disk_in_use: Gauge<f64>,

    // Disk I/O metrics
    disk_read_bytes: Counter<u64>,
    disk_write_bytes: Counter<u64>,
    disk_read_ops: Counter<u64>,
    disk_write_ops: Counter<u64>,

    // Load Average
    load_1: Gauge<f64>,
    load_5: Gauge<f64>,
    load_15: Gauge<f64>,

    // Process count
    processes_total: Gauge<u64>,
    processes_running: Gauge<u64>,
    processes_sleeping: Gauge<u64>,
    processes_stopped: Gauge<u64>,
    processes_zombie: Gauge<u64>,

    // TCP connection states
    tcp_connections_established: Gauge<u64>,
    tcp_connections_close_wait: Gauge<u64>,
    tcp_connections_time_wait: Gauge<u64>,
    tcp_connections_fin_wait: Gauge<u64>,
    tcp_connections_syn: Gauge<u64>,

    // Braintrust extensions
    process_memory: Gauge<u64>,
    process_threads: Gauge<u64>,

    // System info
    system: System,

    // Previous CPU stats for Linux detailed metrics
    #[cfg(target_os = "linux")]
    prev_cpu_times: Option<CpuTimes>,

    // Previous disk I/O stats for rate calculation
    prev_disk_io: HashMap<String, DiskIoStats>,

    // Cache whether system has NVMe devices
    #[cfg(target_os = "linux")]
    has_nvme: Option<bool>,

    // Track if we've warned about missing nvme-cli
    #[cfg(target_os = "linux")]
    warned_nvme_cli_missing: bool,

    // Track if we've warned about container NVMe access
    #[cfg(target_os = "linux")]
    warned_container_nvme_access: bool,
}

#[cfg(target_os = "linux")]
#[derive(Default)]
struct TcpConnectionStates {
    established: u64,
    syn_sent: u64,
    syn_recv: u64,
    fin_wait1: u64,
    fin_wait2: u64,
    time_wait: u64,
    close: u64,
    close_wait: u64,
    last_ack: u64,
    listen: u64,
    closing: u64,
}

#[cfg(target_os = "linux")]
#[derive(Clone)]
struct CpuTimes {
    user: u64,
    nice: u64,
    system: u64,
    idle: u64,
    iowait: u64,
    irq: u64,
    softirq: u64,
    steal: u64,
    guest: u64,
    guest_nice: u64,
}

#[async_trait]
impl Collector for SystemMetricsCollector {
    /// Collect all system metrics
    async fn collect(&mut self) -> Result<(), util::anyhow::Error> {
        // Refresh system information
        self.system.refresh_all();

        // Collect all metrics
        self.collect_cpu_metrics();
        self.collect_memory_metrics();
        self.collect_network_metrics();
        self.collect_disk_metrics();
        self.collect_load_metrics();
        self.collect_braintrust_metrics();

        // Linux-specific detailed CPU metrics
        #[cfg(target_os = "linux")]
        self.collect_linux_cpu_stats()?;

        // Collect TCP connection states
        #[cfg(target_os = "linux")]
        if let Err(e) = self.collect_tcp_connection_states() {
            log::debug!("Failed to collect TCP connection states: {}", e);
        }

        Ok(())
    }
}

impl SystemMetricsCollector {
    /// Create a new system metrics collector with the given meter
    pub fn new(meter: &Meter) -> Self {
        Self {
            // CPU Metrics
            cpu_user: meter
                .f64_gauge("system.cpu.user")
                .with_description("Percentage of CPU time in user mode")
                .build(),
            cpu_system: meter
                .f64_gauge("system.cpu.system")
                .with_description("Percentage of CPU time in system mode")
                .build(),
            cpu_idle: meter
                .f64_gauge("system.cpu.idle")
                .with_description("Percentage of CPU time idle")
                .build(),
            cpu_iowait: meter
                .f64_gauge("system.cpu.iowait")
                .with_description("Percentage of CPU time waiting for I/O")
                .build(),
            cpu_steal: meter
                .f64_gauge("system.cpu.stolen")
                .with_description("Percentage of CPU time stolen by hypervisor")
                .build(),
            cpu_interrupt: meter
                .f64_gauge("system.cpu.interrupt")
                .with_description("Percentage of CPU time handling interrupts")
                .build(),
            cpu_guest: meter
                .f64_gauge("system.cpu.guest")
                .with_description("Percentage of CPU time running guest OS")
                .build(),
            cpu_nice: meter
                .f64_gauge("system.cpu.nice")
                .with_description("Percentage of CPU time in nice priority")
                .build(),
            cpu_softirq: meter
                .f64_gauge("system.cpu.softirq")
                .with_description("Percentage of CPU time handling soft interrupts")
                .build(),

            // CPU totals
            cpu_user_total: meter
                .u64_counter("system.cpu.user.total")
                .with_description("Total CPU time in user mode")
                .build(),
            cpu_system_total: meter
                .u64_counter("system.cpu.system.total")
                .with_description("Total CPU time in system mode")
                .build(),
            cpu_idle_total: meter
                .u64_counter("system.cpu.idle.total")
                .with_description("Total CPU time idle")
                .build(),
            cpu_iowait_total: meter
                .u64_counter("system.cpu.iowait.total")
                .with_description("Total CPU time waiting for I/O")
                .build(),
            cpu_steal_total: meter
                .u64_counter("system.cpu.steal.total")
                .with_description("Total CPU time stolen")
                .build(),
            cpu_irq_total: meter
                .u64_counter("system.cpu.irq.total")
                .with_description("Total CPU time handling IRQs")
                .build(),
            cpu_softirq_total: meter
                .u64_counter("system.cpu.softirq.total")
                .with_description("Total CPU time handling soft IRQs")
                .build(),
            cpu_nice_total: meter
                .u64_counter("system.cpu.nice.total")
                .with_description("Total CPU time in nice priority")
                .build(),
            cpu_guest_total: meter
                .u64_counter("system.cpu.guest.total")
                .with_description("Total CPU time running guest")
                .build(),
            cpu_guestnice_total: meter
                .u64_counter("system.cpu.guestnice.total")
                .with_description("Total CPU time running nice guest")
                .build(),

            // CPU info
            cpu_num_cores: meter
                .u64_gauge("system.cpu.num_cores")
                .with_description("Number of CPU cores")
                .build(),
            cpu_context_switches: meter
                .u64_counter("system.cpu.context_switches")
                .with_description("CPU context switches")
                .build(),

            // Memory Metrics
            mem_total: meter
                .u64_gauge("system.mem.total")
                .with_description("Total system memory")
                .build(),
            mem_available: meter
                .u64_gauge("system.mem.available")
                .with_description("Available system memory")
                .build(),
            mem_used: meter
                .u64_gauge("system.mem.used")
                .with_description("Used system memory")
                .build(),
            mem_free: meter
                .u64_gauge("system.mem.free")
                .with_description("Free system memory")
                .build(),
            mem_cached: meter
                .u64_gauge("system.mem.cached")
                .with_description("Cached memory")
                .build(),
            mem_buffered: meter
                .u64_gauge("system.mem.buffered")
                .with_description("Buffered memory")
                .build(),
            mem_shared: meter
                .u64_gauge("system.mem.shared")
                .with_description("Shared memory")
                .build(),
            mem_slab: meter
                .u64_gauge("system.mem.slab")
                .with_description("Slab memory")
                .build(),
            mem_page_tables: meter
                .u64_gauge("system.mem.page_tables")
                .with_description("Page tables memory")
                .build(),
            mem_commit_limit: meter
                .u64_gauge("system.mem.commit_limit")
                .with_description("Commit limit")
                .build(),
            mem_committed_as: meter
                .u64_gauge("system.mem.committed_as")
                .with_description("Committed AS")
                .build(),
            mem_usable: meter
                .f64_gauge("system.mem.usable")
                .with_description("Usable memory percentage")
                .build(),
            mem_pct_usable: meter
                .f64_gauge("system.mem.pct_usable")
                .with_description("Percentage of usable memory")
                .build(),

            // Swap Metrics
            swap_total: meter
                .u64_gauge("system.swap.total")
                .with_description("Total swap space")
                .build(),
            swap_used: meter
                .u64_gauge("system.swap.used")
                .with_description("Used swap space")
                .build(),
            swap_free: meter
                .u64_gauge("system.swap.free")
                .with_description("Free swap space")
                .build(),
            swap_pct_used: meter
                .f64_gauge("system.swap.pct_used")
                .with_description("Percentage of swap used")
                .build(),
            swap_pct_free: meter
                .f64_gauge("system.swap.pct_free")
                .with_description("Percentage of swap free")
                .build(),

            // Network Metrics
            net_bytes_sent: meter
                .u64_counter("system.net.bytes_sent")
                .with_description("Network bytes sent")
                .build(),
            net_bytes_recv: meter
                .u64_counter("system.net.bytes_recv")
                .with_description("Network bytes received")
                .build(),
            net_packets_in_count: meter
                .u64_counter("system.net.packets_in.count")
                .with_description("Network packets received")
                .build(),
            net_packets_out_count: meter
                .u64_counter("system.net.packets_out.count")
                .with_description("Network packets sent")
                .build(),
            net_packets_in_error: meter
                .u64_counter("system.net.packets_in.error")
                .with_description("Network packets received with errors")
                .build(),
            net_packets_out_error: meter
                .u64_counter("system.net.packets_out.error")
                .with_description("Network packets sent with errors")
                .build(),

            // Disk Metrics
            disk_total: meter
                .u64_gauge("system.disk.total")
                .with_description("Total disk space")
                .build(),
            disk_used: meter
                .u64_gauge("system.disk.used")
                .with_description("Used disk space")
                .build(),
            disk_free: meter
                .u64_gauge("system.disk.free")
                .with_description("Free disk space")
                .build(),
            disk_in_use: meter
                .f64_gauge("system.disk.in_use")
                .with_description("Percentage of disk in use")
                .build(),

            // Disk I/O metrics
            disk_read_bytes: meter
                .u64_counter("system.disk.read_bytes")
                .with_description("Bytes read from disk")
                .build(),
            disk_write_bytes: meter
                .u64_counter("system.disk.write_bytes")
                .with_description("Bytes written to disk")
                .build(),
            disk_read_ops: meter
                .u64_counter("system.disk.read_ops")
                .with_description("Disk read operations")
                .build(),
            disk_write_ops: meter
                .u64_counter("system.disk.write_ops")
                .with_description("Disk write operations")
                .build(),

            // Load Average
            load_1: meter
                .f64_gauge("system.load.1")
                .with_description("1 minute load average")
                .build(),
            load_5: meter
                .f64_gauge("system.load.5")
                .with_description("5 minute load average")
                .build(),
            load_15: meter
                .f64_gauge("system.load.15")
                .with_description("15 minute load average")
                .build(),

            // Process count
            processes_total: meter
                .u64_gauge("system.processes.total")
                .with_description("Total number of processes")
                .build(),
            processes_running: meter
                .u64_gauge("system.processes.running")
                .with_description("Number of running processes")
                .build(),
            processes_sleeping: meter
                .u64_gauge("system.processes.sleeping")
                .with_description("Number of sleeping processes")
                .build(),
            processes_stopped: meter
                .u64_gauge("system.processes.stopped")
                .with_description("Number of stopped processes")
                .build(),
            processes_zombie: meter
                .u64_gauge("system.processes.zombie")
                .with_description("Number of zombie processes")
                .build(),

            // TCP connection states
            tcp_connections_established: meter
                .u64_gauge("system.tcp.connections.established")
                .with_description("Number of established TCP connections")
                .build(),
            tcp_connections_close_wait: meter
                .u64_gauge("system.tcp.connections.close_wait")
                .with_description("Number of TCP connections in CLOSE_WAIT state")
                .build(),
            tcp_connections_time_wait: meter
                .u64_gauge("system.tcp.connections.time_wait")
                .with_description("Number of TCP connections in TIME_WAIT state")
                .build(),
            tcp_connections_fin_wait: meter
                .u64_gauge("system.tcp.connections.fin_wait")
                .with_description("Number of TCP connections in FIN_WAIT states")
                .build(),
            tcp_connections_syn: meter
                .u64_gauge("system.tcp.connections.syn")
                .with_description("Number of TCP connections in SYN states")
                .build(),

            // Braintrust extensions
            process_memory: meter
                .u64_gauge("system.process.memory")
                .with_description("Memory usage of the current process")
                .build(),
            process_threads: meter
                .u64_gauge("system.process.threads")
                .with_description("Number of threads in the current process")
                .build(),

            system: System::new_all(),
            #[cfg(target_os = "linux")]
            prev_cpu_times: None,
            prev_disk_io: HashMap::new(),
            #[cfg(target_os = "linux")]
            has_nvme: None,
            #[cfg(target_os = "linux")]
            warned_nvme_cli_missing: false,
            #[cfg(target_os = "linux")]
            warned_container_nvme_access: false,
        }
    }

    /// Check if the system has NVMe devices (Linux only)
    #[cfg(target_os = "linux")]
    pub fn has_nvme_devices(&mut self) -> bool {
        self.detect_nvme_devices()
    }

    /// Check if the system has NVMe devices (non-Linux returns false)
    #[cfg(not(target_os = "linux"))]
    pub fn has_nvme_devices(&mut self) -> bool {
        false
    }

    fn collect_cpu_metrics(&self) {
        // Get CPU count
        let cpu_count = self.system.cpus().len() as u64;
        self.cpu_num_cores.record(cpu_count, &[]);

        // Get global CPU usage
        let global_cpu = self.system.global_cpu_usage();
        self.cpu_user.record(global_cpu as f64, &[]);

        // Note: sysinfo doesn't provide detailed CPU breakdown like iowait, steal, etc.
        // For those, we need to read from /proc/stat on Linux (see collect_linux_cpu_stats)
    }

    fn collect_memory_metrics(&self) {
        let total = self.system.total_memory();
        let available = self.system.available_memory();
        let used = self.system.used_memory();
        let free = self.system.free_memory();

        self.mem_total.record(total, &[]);
        self.mem_available.record(available, &[]);
        self.mem_used.record(used, &[]);
        self.mem_free.record(free, &[]);

        // Calculate usable memory percentage
        if total > 0 {
            let pct_usable = (available as f64 / total as f64) * 100.0;
            self.mem_pct_usable.record(pct_usable, &[]);
            self.mem_usable.record(available as f64, &[]);
        }

        // Swap metrics
        let swap_total = self.system.total_swap();
        let swap_used = self.system.used_swap();
        let swap_free = self.system.free_swap();

        self.swap_total.record(swap_total, &[]);
        self.swap_used.record(swap_used, &[]);
        self.swap_free.record(swap_free, &[]);

        if swap_total > 0 {
            let pct_used = (swap_used as f64 / swap_total as f64) * 100.0;
            let pct_free = (swap_free as f64 / swap_total as f64) * 100.0;
            self.swap_pct_used.record(pct_used, &[]);
            self.swap_pct_free.record(pct_free, &[]);
        }
    }

    fn collect_network_metrics(&self) {
        let mut total_bytes_sent = 0u64;
        let mut total_bytes_recv = 0u64;
        let mut total_packets_sent = 0u64;
        let mut total_packets_recv = 0u64;
        let mut total_errors_sent = 0u64;
        let mut total_errors_recv = 0u64;

        let networks = sysinfo::Networks::new_with_refreshed_list();
        for (interface_name, data) in &networks {
            // Skip loopback interface
            if interface_name == "lo" || interface_name.starts_with("lo") {
                continue;
            }

            total_bytes_sent += data.total_transmitted();
            total_bytes_recv += data.total_received();
            total_packets_sent += data.total_packets_transmitted();
            total_packets_recv += data.total_packets_received();
            total_errors_sent += data.total_errors_on_transmitted();
            total_errors_recv += data.total_errors_on_received();
        }

        // Record totals
        self.net_bytes_sent.add(total_bytes_sent, &[]);
        self.net_bytes_recv.add(total_bytes_recv, &[]);
        self.net_packets_out_count.add(total_packets_sent, &[]);
        self.net_packets_in_count.add(total_packets_recv, &[]);
        self.net_packets_out_error.add(total_errors_sent, &[]);
        self.net_packets_in_error.add(total_errors_recv, &[]);
    }

    fn collect_disk_metrics(&mut self) {
        use sysinfo::{DiskKind, Disks};

        let mut total_disk_size = 0u64;
        let mut total_disk_used = 0u64;
        let mut total_disk_free = 0u64;

        let disks = Disks::new_with_refreshed_list();
        for disk in disks.list() {
            // Skip non-physical disks on some systems
            if matches!(disk.kind(), DiskKind::Unknown(_)) {
                continue;
            }

            let disk_total = disk.total_space();
            let disk_free = disk.available_space();
            let disk_used = disk_total.saturating_sub(disk_free);

            total_disk_size += disk_total;
            total_disk_used += disk_used;
            total_disk_free += disk_free;
        }

        self.disk_total.record(total_disk_size, &[]);
        self.disk_used.record(total_disk_used, &[]);
        self.disk_free.record(total_disk_free, &[]);

        if total_disk_size > 0 {
            let usage_pct = (total_disk_used as f64 / total_disk_size as f64) * 100.0;
            self.disk_in_use.record(usage_pct, &[]);
        }

        // Collect disk I/O stats on Linux
        #[cfg(target_os = "linux")]
        if let Err(e) = self.collect_linux_disk_io() {
            log::error!("Failed to collect disk I/O stats: {}", e);
        }

        // Collect process counts
        self.collect_process_counts();
    }

    #[cfg(target_os = "linux")]
    fn is_running_in_container() -> bool {
        // Check common container indicators
        // 1. Check for .dockerenv file
        if std::path::Path::new("/.dockerenv").exists() {
            return true;
        }

        // 2. Check /proc/1/cgroup for container signatures
        if let Ok(contents) = std::fs::read_to_string("/proc/1/cgroup") {
            if contents.contains("/docker/") || contents.contains("/lxc/") || contents.contains("/kubepods/") {
                return true;
            }
        }

        false
    }

    #[cfg(target_os = "linux")]
    fn detect_nvme_devices(&mut self) -> bool {
        // Check if we've already detected
        if let Some(has_nvme) = self.has_nvme {
            return has_nvme;
        }

        // Check for NVMe devices in /sys/class/nvme/
        let nvme_path = std::path::Path::new("/sys/class/nvme");
        let has_nvme = if nvme_path.exists() {
            // Also verify nvme-cli is available
            std::process::Command::new("which")
                .arg("nvme")
                .output()
                .map(|output| output.status.success())
                .unwrap_or(false)
        } else {
            false
        };

        self.has_nvme = Some(has_nvme);
        has_nvme
    }

    #[cfg(target_os = "linux")]
    fn collect_nvme_stats(&mut self) -> Result<(), util::anyhow::Error> {
        use std::process::Command;

        // Get list of NVMe devices
        let nvme_list_output = match Command::new("nvme")
            .arg("list")
            .arg("-o")
            .arg("json")
            .output()
        {
            Ok(output) => output,
            Err(e) => {
                // If nvme command doesn't exist or fails to execute, just skip
                log::debug!("nvme command not available or failed to execute: {}", e);
                return Ok(());
            }
        };

        if !nvme_list_output.status.success() {
            // nvme command exists but failed to run properly
            let stderr = String::from_utf8_lossy(&nvme_list_output.stderr);

            // Check if this is a permission error in a container
            if !self.warned_container_nvme_access &&
               Self::is_running_in_container() &&
               (stderr.contains("Permission denied") ||
                stderr.contains("Operation not permitted") ||
                stderr.contains("Failed to open")) {
                log::warn!(
                    "Running in container without NVMe device access. To enable NVMe metrics, run container with: \n\
                    --device=/dev/nvme0n1 --device=/dev/nvme1n1 -v /sys/class/nvme:/sys/class/nvme:ro\n\
                    Or use --privileged mode. This warning will only be shown once."
                );
                self.warned_container_nvme_access = true;
            } else {
                log::debug!("nvme list command failed: {}", stderr);
            }
            return Ok(());
        }

        // Parse JSON output
        let nvme_list: serde_json::Value = match serde_json::from_slice(&nvme_list_output.stdout) {
            Ok(json) => json,
            Err(e) => {
                log::debug!("Failed to parse nvme list JSON: {}", e);
                return Ok(());
            }
        };

        if let Some(devices) = nvme_list["Devices"].as_array() {
            for device in devices {
                if let Some(device_path) = device["DevicePath"].as_str() {
                    // Get smart-log for each device to get read/write stats
                    let smart_log_output = match Command::new("nvme")
                        .arg("smart-log")
                        .arg(device_path)
                        .arg("-o")
                        .arg("json")
                        .output()
                    {
                        Ok(output) => output,
                        Err(e) => {
                            log::debug!("Failed to get smart-log for {}: {}", device_path, e);
                            continue;
                        }
                    };

                    if smart_log_output.status.success() {
                        let smart_log: serde_json::Value = match serde_json::from_slice(&smart_log_output.stdout) {
                            Ok(json) => json,
                            Err(e) => {
                                log::debug!("Failed to parse smart-log JSON for {}: {}", device_path, e);
                                continue;
                            }
                        };

                        // Extract the metrics we need
                        // Note: NVMe reports in 512-byte units for data_units_read/written
                        let data_units_read = smart_log["data_units_read"]
                            .as_str()
                            .and_then(|s| s.replace(",", "").parse::<u64>().ok())
                            .unwrap_or(0);
                        let data_units_written = smart_log["data_units_written"]
                            .as_str()
                            .and_then(|s| s.replace(",", "").parse::<u64>().ok())
                            .unwrap_or(0);

                        // NVMe data units are typically 512 * 1000 bytes
                        let bytes_read = data_units_read * 512 * 1000;
                        let bytes_written = data_units_written * 512 * 1000;

                        // Extract I/O operation counts from smart log
                        let read_ops = smart_log["host_read_commands"]
                            .as_u64()
                            .or_else(|| smart_log["host_read_commands"]
                                .as_str()
                                .and_then(|s| s.replace(",", "").parse::<u64>().ok()))
                            .unwrap_or(0);
                        let write_ops = smart_log["host_write_commands"]
                            .as_u64()
                            .or_else(|| smart_log["host_write_commands"]
                                .as_str()
                                .and_then(|s| s.replace(",", "").parse::<u64>().ok()))
                            .unwrap_or(0);

                        // Extract device name from path (e.g., /dev/nvme0n1 -> nvme0n1)
                        let device_name = device_path
                            .strip_prefix("/dev/")
                            .unwrap_or(device_path);

                        // Calculate deltas if we have previous data
                        if let Some(prev) = self.prev_disk_io.get(device_name) {
                            let read_bytes_delta = bytes_read.saturating_sub(prev.read_bytes);
                            let write_bytes_delta = bytes_written.saturating_sub(prev.write_bytes);
                            let reads_delta = read_ops.saturating_sub(prev.read_count);
                            let writes_delta = write_ops.saturating_sub(prev.write_count);

                            let labels = vec![opentelemetry::KeyValue::new(
                                "device",
                                device_name.to_string(),
                            )];

                            self.disk_read_bytes.add(read_bytes_delta, &labels);
                            self.disk_write_bytes.add(write_bytes_delta, &labels);
                            self.disk_read_ops.add(reads_delta, &labels);
                            self.disk_write_ops.add(writes_delta, &labels);
                        }

                        // Store current values for next iteration
                        self.prev_disk_io.insert(
                            device_name.to_string(),
                            DiskIoStats {
                                read_bytes: bytes_read,
                                write_bytes: bytes_written,
                                read_count: read_ops,
                                write_count: write_ops,
                            },
                        );
                    } else {
                        // Handle permission errors specifically for containers
                        let stderr = String::from_utf8_lossy(&smart_log_output.stderr);
                        if !self.warned_container_nvme_access &&
                           Self::is_running_in_container() &&
                           (stderr.contains("Permission denied") ||
                            stderr.contains("Operation not permitted")) {
                            log::warn!(
                                "Running in container without NVMe device access. To enable NVMe metrics, run container with: \n\
                                --device=/dev/nvme0n1 --device=/dev/nvme1n1 -v /sys/class/nvme:/sys/class/nvme:ro\n\
                                Or use --privileged mode. This warning will only be shown once."
                            );
                            self.warned_container_nvme_access = true;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    #[cfg(target_os = "linux")]
    fn collect_linux_disk_io(&mut self) -> Result<(), util::anyhow::Error> {
        use std::fs::File;
        use std::io::{BufRead, BufReader};

        // Check if system has NVMe devices but nvme-cli is missing
        let nvme_path = std::path::Path::new("/sys/class/nvme");
        if nvme_path.exists() && !self.warned_nvme_cli_missing {
            // Check if nvme-cli is available
            let nvme_cli_available = std::process::Command::new("which")
                .arg("nvme")
                .output()
                .map(|output| output.status.success())
                .unwrap_or(false);

            if !nvme_cli_available {
                log::warn!("NVMe devices detected but nvme-cli is not installed. Disk stats may be inaccurate. Install nvme-cli for better metrics.");
                self.warned_nvme_cli_missing = true;
            }
        }

        // First, try to collect NVMe stats if available
        if self.detect_nvme_devices() {
            if let Err(e) = self.collect_nvme_stats() {
                log::warn!("Failed to collect NVMe stats via nvme-cli, falling back to /proc/diskstats: {}", e);
            }
        }

        let file = File::open("/proc/diskstats")?;
        let reader = BufReader::new(file);

        for line in reader.lines() {
            let line = line?;
            let parts: Vec<&str> = line.split_whitespace().collect();

            if parts.len() < 14 {
                continue;
            }

            let device_name = parts[2];

            // Skip loop devices, ram devices, etc
            if device_name.starts_with("loop") || device_name.starts_with("ram") {
                continue;
            }

            // Skip NVMe devices if we've already collected them via nvme-cli
            if self.has_nvme.unwrap_or(false) && device_name.starts_with("nvme") {
                continue;
            }

            // Fields from /proc/diskstats:
            // 3: reads completed
            // 5: sectors read (multiply by 512 for bytes)
            // 7: writes completed
            // 9: sectors written (multiply by 512 for bytes)

            let reads = parts[3].parse::<u64>().unwrap_or(0);
            let read_sectors = parts[5].parse::<u64>().unwrap_or(0);
            let writes = parts[7].parse::<u64>().unwrap_or(0);
            let write_sectors = parts[9].parse::<u64>().unwrap_or(0);

            let read_bytes = read_sectors * 512;
            let write_bytes = write_sectors * 512;

            // Calculate deltas if we have previous data
            if let Some(prev) = self.prev_disk_io.get(device_name) {
                let read_bytes_delta = read_bytes.saturating_sub(prev.read_bytes);
                let write_bytes_delta = write_bytes.saturating_sub(prev.write_bytes);
                let reads_delta = reads.saturating_sub(prev.read_count);
                let writes_delta = writes.saturating_sub(prev.write_count);

                // Add to counters with device label
                let labels = vec![opentelemetry::KeyValue::new(
                    "device",
                    device_name.to_string(),
                )];

                self.disk_read_bytes.add(read_bytes_delta, &labels);
                self.disk_write_bytes.add(write_bytes_delta, &labels);
                self.disk_read_ops.add(reads_delta, &labels);
                self.disk_write_ops.add(writes_delta, &labels);
            }

            // Store current values for next iteration
            self.prev_disk_io.insert(
                device_name.to_string(),
                DiskIoStats {
                    read_bytes,
                    write_bytes,
                    read_count: reads,
                    write_count: writes,
                },
            );
        }

        Ok(())
    }

    fn collect_process_counts(&self) {
        use sysinfo::ProcessStatus;

        let mut total = 0u64;
        let mut running = 0u64;
        let mut sleeping = 0u64;
        let mut stopped = 0u64;
        let mut zombie = 0u64;

        for (_pid, process) in self.system.processes() {
            total += 1;

            match process.status() {
                ProcessStatus::Run => running += 1,
                ProcessStatus::Sleep | ProcessStatus::Idle => sleeping += 1,
                ProcessStatus::Stop => stopped += 1,
                ProcessStatus::Zombie => zombie += 1,
                _ => {} // Other states we don't track
            }
        }

        self.processes_total.record(total, &[]);
        self.processes_running.record(running, &[]);
        self.processes_sleeping.record(sleeping, &[]);
        self.processes_stopped.record(stopped, &[]);
        self.processes_zombie.record(zombie, &[]);
    }

    fn collect_load_metrics(&self) {
        let load_avg = System::load_average();
        self.load_1.record(load_avg.one, &[]);
        self.load_5.record(load_avg.five, &[]);
        self.load_15.record(load_avg.fifteen, &[]);
    }

    fn collect_braintrust_metrics(&self) {
        let memory_and_threads = get_memory_usage_with_children(&self.system);
        match memory_and_threads {
            Ok(memory_and_threads) => {
                self.process_memory.record(memory_and_threads.memory, &[]);
                self.process_threads
                    .record(memory_and_threads.threads.unwrap_or(0) as u64, &[]);
            }
            Err(e) => {
                log::warn!("Failed to get memory and threads usage: {:?}", e);
            }
        }
    }

    #[cfg(target_os = "linux")]
    fn collect_linux_cpu_stats(&mut self) -> Result<(), util::anyhow::Error> {
        // Read /proc/stat for detailed CPU metrics
        let stat_content = std::fs::read_to_string("/proc/stat")?;

        for line in stat_content.lines() {
            if line.starts_with("cpu ") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 11 {
                    let current_times = CpuTimes {
                        user: parts[1].parse().unwrap_or(0),
                        nice: parts[2].parse().unwrap_or(0),
                        system: parts[3].parse().unwrap_or(0),
                        idle: parts[4].parse().unwrap_or(0),
                        iowait: parts[5].parse().unwrap_or(0),
                        irq: parts[6].parse().unwrap_or(0),
                        softirq: parts[7].parse().unwrap_or(0),
                        steal: parts[8].parse().unwrap_or(0),
                        guest: parts[9].parse().unwrap_or(0),
                        guest_nice: parts[10].parse().unwrap_or(0),
                    };

                    // Calculate percentages if we have previous stats
                    if let Some(prev) = &self.prev_cpu_times {
                        let total_delta = (current_times.user - prev.user)
                            + (current_times.nice - prev.nice)
                            + (current_times.system - prev.system)
                            + (current_times.idle - prev.idle)
                            + (current_times.iowait - prev.iowait)
                            + (current_times.irq - prev.irq)
                            + (current_times.softirq - prev.softirq)
                            + (current_times.steal - prev.steal)
                            + (current_times.guest - prev.guest)
                            + (current_times.guest_nice - prev.guest_nice);

                        if total_delta > 0 {
                            let total_delta_f = total_delta as f64;
                            self.cpu_user.record(
                                ((current_times.user - prev.user) as f64 / total_delta_f) * 100.0,
                                &[],
                            );
                            self.cpu_nice.record(
                                ((current_times.nice - prev.nice) as f64 / total_delta_f) * 100.0,
                                &[],
                            );
                            self.cpu_system.record(
                                ((current_times.system - prev.system) as f64 / total_delta_f)
                                    * 100.0,
                                &[],
                            );
                            self.cpu_idle.record(
                                ((current_times.idle - prev.idle) as f64 / total_delta_f) * 100.0,
                                &[],
                            );
                            self.cpu_iowait.record(
                                ((current_times.iowait - prev.iowait) as f64 / total_delta_f)
                                    * 100.0,
                                &[],
                            );
                            self.cpu_interrupt.record(
                                ((current_times.irq - prev.irq) as f64 / total_delta_f) * 100.0,
                                &[],
                            );
                            self.cpu_softirq.record(
                                ((current_times.softirq - prev.softirq) as f64 / total_delta_f)
                                    * 100.0,
                                &[],
                            );
                            self.cpu_steal.record(
                                ((current_times.steal - prev.steal) as f64 / total_delta_f) * 100.0,
                                &[],
                            );
                            self.cpu_guest.record(
                                ((current_times.guest - prev.guest) as f64 / total_delta_f) * 100.0,
                                &[],
                            );
                        }
                    }

                    // Update totals (convert jiffies to milliseconds - usually 1 jiffy = 10ms)
                    let jiffy_to_ms = 10u64;
                    self.cpu_user_total.add(
                        (current_times.user
                            - self.prev_cpu_times.as_ref().map(|p| p.user).unwrap_or(0))
                            * jiffy_to_ms,
                        &[],
                    );
                    self.cpu_system_total.add(
                        (current_times.system
                            - self.prev_cpu_times.as_ref().map(|p| p.system).unwrap_or(0))
                            * jiffy_to_ms,
                        &[],
                    );
                    self.cpu_idle_total.add(
                        (current_times.idle
                            - self.prev_cpu_times.as_ref().map(|p| p.idle).unwrap_or(0))
                            * jiffy_to_ms,
                        &[],
                    );
                    self.cpu_iowait_total.add(
                        (current_times.iowait
                            - self.prev_cpu_times.as_ref().map(|p| p.iowait).unwrap_or(0))
                            * jiffy_to_ms,
                        &[],
                    );
                    self.cpu_steal_total.add(
                        (current_times.steal
                            - self.prev_cpu_times.as_ref().map(|p| p.steal).unwrap_or(0))
                            * jiffy_to_ms,
                        &[],
                    );
                    self.cpu_irq_total.add(
                        (current_times.irq
                            - self.prev_cpu_times.as_ref().map(|p| p.irq).unwrap_or(0))
                            * jiffy_to_ms,
                        &[],
                    );
                    self.cpu_softirq_total.add(
                        (current_times.softirq
                            - self.prev_cpu_times.as_ref().map(|p| p.softirq).unwrap_or(0))
                            * jiffy_to_ms,
                        &[],
                    );
                    self.cpu_nice_total.add(
                        (current_times.nice
                            - self.prev_cpu_times.as_ref().map(|p| p.nice).unwrap_or(0))
                            * jiffy_to_ms,
                        &[],
                    );
                    self.cpu_guest_total.add(
                        (current_times.guest
                            - self.prev_cpu_times.as_ref().map(|p| p.guest).unwrap_or(0))
                            * jiffy_to_ms,
                        &[],
                    );
                    self.cpu_guestnice_total.add(
                        (current_times.guest_nice
                            - self
                                .prev_cpu_times
                                .as_ref()
                                .map(|p| p.guest_nice)
                                .unwrap_or(0))
                            * jiffy_to_ms,
                        &[],
                    );

                    self.prev_cpu_times = Some(current_times);
                }
                break;
            }
        }

        // Get context switches from /proc/stat
        for line in stat_content.lines() {
            if line.starts_with("ctxt ") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 2 {
                    if let Ok(ctxt) = parts[1].parse::<u64>() {
                        self.cpu_context_switches.add(ctxt, &[]);
                    }
                }
                break;
            }
        }

        // Get additional memory stats from /proc/meminfo
        let meminfo = std::fs::read_to_string("/proc/meminfo")?;
        let mut values = HashMap::new();
        for line in meminfo.lines() {
            let parts: Vec<&str> = line.split_whitespace().collect();
            if parts.len() >= 2 {
                let key = parts[0].trim_end_matches(':');
                if let Ok(value) = parts[1].parse::<u64>() {
                    // Values in /proc/meminfo are in KB
                    values.insert(key, value * 1024);
                }
            }
        }

        // Record additional memory metrics
        if let Some(&buffers) = values.get("Buffers") {
            self.mem_buffered.record(buffers, &[]);
        }
        if let Some(&cached) = values.get("Cached") {
            self.mem_cached.record(cached, &[]);
        }
        if let Some(&shmem) = values.get("Shmem") {
            self.mem_shared.record(shmem, &[]);
        }
        if let Some(&slab) = values.get("Slab") {
            self.mem_slab.record(slab, &[]);
        }
        if let Some(&page_tables) = values.get("PageTables") {
            self.mem_page_tables.record(page_tables, &[]);
        }
        if let Some(&commit_limit) = values.get("CommitLimit") {
            self.mem_commit_limit.record(commit_limit, &[]);
        }
        if let Some(&committed_as) = values.get("Committed_AS") {
            self.mem_committed_as.record(committed_as, &[]);
        }

        Ok(())
    }

    #[cfg(target_os = "linux")]
    fn collect_tcp_connection_states(&self) -> Result<(), util::anyhow::Error> {
        let tcp_states = self.read_tcp_connection_states()?;

        // Record TCP connection states
        self.tcp_connections_established
            .record(tcp_states.established, &[]);
        self.tcp_connections_close_wait
            .record(tcp_states.close_wait, &[]);
        self.tcp_connections_time_wait
            .record(tcp_states.time_wait, &[]);
        self.tcp_connections_fin_wait
            .record(tcp_states.fin_wait1 + tcp_states.fin_wait2, &[]);
        self.tcp_connections_syn
            .record(tcp_states.syn_sent + tcp_states.syn_recv, &[]);

        Ok(())
    }

    #[cfg(target_os = "linux")]
    fn read_tcp_connection_states(&self) -> Result<TcpConnectionStates, util::anyhow::Error> {
        use std::fs;

        let mut states = TcpConnectionStates::default();

        // Read TCP connections from /proc/net/tcp (IPv4)
        if let Ok(content) = fs::read_to_string("/proc/net/tcp") {
            for line in content.lines().skip(1) {
                // Skip header
                if let Some(state_str) = line.split_whitespace().nth(3) {
                    if let Ok(state) = u8::from_str_radix(state_str, 16) {
                        match state {
                            1 => states.established += 1,
                            2 => states.syn_sent += 1,
                            3 => states.syn_recv += 1,
                            4 => states.fin_wait1 += 1,
                            5 => states.fin_wait2 += 1,
                            6 => states.time_wait += 1,
                            7 => states.close += 1,
                            8 => states.close_wait += 1,
                            9 => states.last_ack += 1,
                            10 => states.listen += 1,
                            11 => states.closing += 1,
                            _ => {}
                        }
                    }
                }
            }
        }

        // Read TCP connections from /proc/net/tcp6 (IPv6)
        if let Ok(content) = fs::read_to_string("/proc/net/tcp6") {
            for line in content.lines().skip(1) {
                // Skip header
                if let Some(state_str) = line.split_whitespace().nth(3) {
                    if let Ok(state) = u8::from_str_radix(state_str, 16) {
                        match state {
                            1 => states.established += 1,
                            2 => states.syn_sent += 1,
                            3 => states.syn_recv += 1,
                            4 => states.fin_wait1 += 1,
                            5 => states.fin_wait2 += 1,
                            6 => states.time_wait += 1,
                            7 => states.close += 1,
                            8 => states.close_wait += 1,
                            9 => states.last_ack += 1,
                            10 => states.listen += 1,
                            11 => states.closing += 1,
                            _ => {}
                        }
                    }
                }
            }
        }

        Ok(states)
    }
}
