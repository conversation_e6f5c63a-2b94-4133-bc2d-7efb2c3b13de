#!/usr/bin/env python

import argparse
import dataclasses
import datetime
import json
import os
import shlex
import socket
import subprocess
import sys
from typing import Tuple

import psutil
from braintrust_local import healthcheck

SCRIPT_DIR = os.path.dirname(__file__)
REPO_ROOT = os.path.abspath(os.path.join(SCRIPT_DIR, ".."))
PROC_INFO_DIR = os.path.join(REPO_ROOT, "services", "data", "bt-proc-info")
LOGS_DIR = os.path.join(REPO_ROOT, "services", "data", "bt-logs")

# ANSI color codes for service logs
SERVICE_COLORS = [
    "\033[31m",  # Red
    "\033[32m",  # Green
    "\033[33m",  # Yellow
    "\033[34m",  # Blue
    "\033[35m",  # Magenta
    "\033[36m",  # <PERSON><PERSON>
    "\033[91m",  # <PERSON> Red
    "\033[92m",  # <PERSON>
    "\033[93m",  # <PERSON>
    "\033[94m",  # <PERSON>
    "\033[95m",  # <PERSON>
    "\033[96m",  # <PERSON>
    "\033[38;5;208m",  # <PERSON>
    "\033[38;5;141m",  # Purple
    "\033[38;5;46m",  # Bright Green
    "\033[38;5;196m",  # Bright Red
    "\033[38;5;226m",  # Bright Yellow
    "\033[38;5;51m",  # Bright Cyan
    "\033[38;5;201m",  # Bright Magenta
    "\033[38;5;118m",  # Light Green
    "\033[38;5;214m",  # Light Orange
    "\033[38;5;177m",  # Light Purple
    "\033[38;5;39m",  # Light Blue
    "\033[38;5;154m",  # Light Yellow
]
RESET_COLOR = "\033[0m"


def eprint(*args, **kwargs):
    print(*args, file=sys.stderr, **kwargs)


def is_port_open(host, port):
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
        sock.settimeout(1)  # Set a timeout for the connection attempt
        result = sock.connect_ex((host, port))
        return result == 0  # Returns True if the port is open, False otherwise


class ProcessDef:
    """Simple capturing wrapper around args and **kwargs to define a process to execute."""

    def __init__(self, args, compute_extra_args_kwargs=None, **kwargs):
        self._args = args
        self._compute_extra_args_kwargs = compute_extra_args_kwargs
        self._kwargs = kwargs

    def get_args_kwargs(self):
        args = self._args
        kwargs = self._kwargs
        if self._compute_extra_args_kwargs:
            extra_args, extra_kwargs = self._compute_extra_args_kwargs()
            args = args + extra_args
            kwargs = {**kwargs, **extra_kwargs}
        return args, kwargs


@dataclasses.dataclass
class ManagedProcess:
    prerequisites: Tuple[ProcessDef, ...] = tuple()
    mains: Tuple[ProcessDef, ...] = tuple()


def make_js_dev_managed_proc(package_name, main_compute_extra_args_kwargs=None, dev_command="dev", full_build=False):
    return ManagedProcess(
        prerequisites=(
            ProcessDef(
                ["pnpm", "build", "--filter", package_name if full_build else f"{package_name}^..."], cwd=REPO_ROOT
            ),
        ),
        mains=(
            ProcessDef(
                ["pnpm", "--filter", package_name, dev_command],
                cwd=REPO_ROOT,
                compute_extra_args_kwargs=main_compute_extra_args_kwargs,
            ),
        ),
    )


def make_js_prod_managed_proc(package_name):
    return ManagedProcess(
        prerequisites=(ProcessDef(["pnpm", "build", "--filter", package_name], cwd=REPO_ROOT),),
        mains=(ProcessDef(["pnpm", "start", "--filter", package_name], cwd=REPO_ROOT),),
    )


def make_brainstore_managed_proc(release=False):
    # check if 'watchexec' is installed. If not, use 'cargo run' instead.
    try:
        watchexec_installed = subprocess.run(["watchexec", "-V"], capture_output=True, text=True).returncode == 0
    except FileNotFoundError:
        watchexec_installed = False
    release_args = ["--release"] if release else []
    local_otlp_args = ["--local-otlp"] if is_port_open("localhost", 4318) else []
    cargo_run_cmd = (
        [
            "cargo",
            "run",
        ]
        + release_args
        + [
            "--bin",
            "brainstore",
            "web",
            "--config",
            "examples/braintrust/brainstore.yaml",
            "--schema",
            "examples/braintrust/schema.json",
            "-v",
            "--suppress-verbose-info",
            "--pretty-logging=true",
        ]
        + local_otlp_args
    )
    if watchexec_installed:
        args = [
            "watchexec",
            "-e",
            "rs,toml,yaml,json",
            "-r",
            " ".join(cargo_run_cmd),
        ]
    else:
        print(
            "watchexec not installed, using cargo run instead. You can install it with `cargo install watchexec-cli`, and rust changes will automatically refresh brainstore",
            file=sys.stderr,
        )
        args = cargo_run_cmd

    # NOTE: Make sure to copy changes over to `generate_docker_compose.py` so that they're used
    # in Docker tests. At some point we should factor out these out to a shared python library.
    brainstore_env = {
        **os.environ,
        "AWS_ENDPOINT_URL": "http://localhost:10000",
        "AWS_ACCESS_KEY_ID": "minio_root_user",
        "AWS_SECRET_ACCESS_KEY": "minio_root_password",
        "AWS_ALLOW_HTTP": "true",
        # No need to stress out dev machines with a large cache.
        "BRAINSTORE_OBJECT_STORE_CACHE_MEMORY_LIMIT": "1MB",
        "BRAINSTORE_OBJECT_STORE_CACHE_FILE_SIZE": "1GB",
        "BRAINSTORE_LOCKS_MANAGER_ENABLE_BOOKKEEPING": "true",
        # Don't check the memory limit / timeout for realtime WAL entries, so
        # that unit tests don't randomly hit the limits.
        "BRAINSTORE_SKIP_REALTIME_WAL_MEMORY_LIMIT": "true",
        "BRAINSTORE_REALTIME_READ_TIMEOUT_MS": "0",
        "BRAINSTORE_VACUUM_OBJECT_ALL": "true",
        "BRAINSTORE_VACUUM_INDEX_DELETION_GRACE_PERIOD_SECONDS": "2",
        "BRAINSTORE_VACUUM_INDEX_LAST_WRITTEN_SLOP_SECONDS": "1",
        "BRAINSTORE_VACUUM_INDEX_PERIOD_SECONDS": "2",
        "BRAINSTORE_BACKGROUND_VACUUM_SLEEP_SECONDS": "1",
        # No need to send telemetry to the control plane for local development
        "BRAINSTORE_CONTROL_PLANE_TELEMETRY": "",
    }

    return ManagedProcess(
        prerequisites=(
            ProcessDef(["make", "brainstore/examples/braintrust/schema.json"], cwd=REPO_ROOT),
            ProcessDef(
                ["cargo", "build", "--bin", "brainstore"] + release_args,
                cwd=os.path.join(REPO_ROOT, "brainstore"),
                env=brainstore_env,
            ),
            ProcessDef(["make", "brainstore-init-buckets-tests"], cwd=REPO_ROOT, env=brainstore_env),
        ),
        mains=(
            ProcessDef(
                args,
                cwd=os.path.join(REPO_ROOT, "brainstore"),
                env=brainstore_env,
            ),
        ),
    )


WEBAPP_PACKAGE = "braintrustdata"
WEBAPP_DIR = os.path.join(REPO_ROOT, "app")
REALTIME_PACKAGE = "@braintrust/realtime"
REALTIME_DIR = os.path.join(REPO_ROOT, "realtime")
API_TS_PACKAGE = "@braintrust/api-ts"
API_TS_DIR = os.path.join(REPO_ROOT, "api-ts")


SERVICES = {
    "webapp": dict(
        modes=[
            ("dev", make_js_dev_managed_proc(WEBAPP_PACKAGE)),
            ("prod", make_js_prod_managed_proc(WEBAPP_PACKAGE)),
        ],
        healthcheck_url="http://localhost:3000/login",
    ),
    "realtime": dict(
        modes=[
            ("dev", make_js_dev_managed_proc(REALTIME_PACKAGE)),
        ],
        healthcheck_url="http://localhost:8788",
    ),
    "api-ts": dict(
        modes=[
            (
                "dev",
                make_js_dev_managed_proc(API_TS_PACKAGE, dev_command="dev:serve", full_build=True),
            ),
        ],
        healthcheck_url="http://localhost:8000",
    ),
    "brainstore": dict(
        modes=[
            ("prod", make_brainstore_managed_proc(release=True)),  # Release should be the default
            ("dev", make_brainstore_managed_proc()),
        ],
        healthcheck_url="http://localhost:4000/healthcheck",
    ),
    "test-proxy": dict(
        modes=[
            (
                "dev",
                make_js_dev_managed_proc(API_TS_PACKAGE, dev_command="run:test-proxy", full_build=True),
            ),
        ],
        healthcheck_url="http://localhost:8001",
    ),
    "watch": dict(
        modes=[
            (
                "no-webapp",
                ManagedProcess(
                    mains=(
                        ProcessDef(
                            ["pnpm", "watch", "--filter", "!braintrustdata"],
                            cwd=REPO_ROOT,
                        ),
                    ),
                ),
            ),
        ],
    ),
}

BT_SERVICES_LOCKFILE = "_BT_SERVICES_LOCKFILE"
assert BT_SERVICES_LOCKFILE not in SERVICES


def _get_services_helper(args):
    assert not (args.include and args.exclude), "Cannot specify both --include and --exclude"
    if args.include:
        return args.include
    if args.exclude:
        return [s for s in SERVICES.keys() if s not in args.exclude]
    return list(SERVICES.keys())


def _get_services(args):
    services = _get_services_helper(args)
    for s in services:
        assert s in SERVICES, s
    return services


def _is_pid_running(pid):
    try:
        return psutil.Process(pid).is_running()
    except psutil.NoSuchProcess:
        return False


def _cleanup_pid(pid):
    for proc in psutil.process_iter():
        try:
            if os.getsid(proc.pid) == pid:
                proc.terminate()
        except (psutil.NoSuchProcess, ProcessLookupError):
            continue


def run_start(args):
    services = _get_services(args)
    service_managed_procs = []
    for service in services:
        service_mode = getattr(args, f"{service.replace('-', '_')}_mode")
        managed_procs = [s for s in SERVICES[service]["modes"] if s[0] == service_mode]
        assert len(managed_procs) == 1, f"Not exactly one match for service mode {service_mode}. Found {managed_procs}"
        service_managed_procs.append(managed_procs[0][1])

    for service, managed_proc in zip(services, service_managed_procs):
        # Check if there is an existing proc entry for the service.
        PROC_INFO_FULLPATH = os.path.abspath(os.path.join(PROC_INFO_DIR, service))
        try:
            with open(PROC_INFO_FULLPATH) as f:
                proc_info = json.load(f)
        except FileNotFoundError:
            proc_info = None

        # If there is an existing such process running, print out its
        # information and skip over this service.
        if proc_info:
            running_pids = [pid for pid in proc_info["pids"] if _is_pid_running(pid)]
            if running_pids:
                eprint(
                    f"Skipping over {service}, as there is a process info entry at {PROC_INFO_FULLPATH} with running processes {running_pids}: {proc_info}. To stop/restart the service, you may use this util, or manually cleanup the processes"
                )
                continue

        # Start the service.
        assert managed_proc.mains, f"Process definition for {service} must have mains"
        if managed_proc.prerequisites:
            eprint(f"Running prerequisites for {service}")
            for proc_def in managed_proc.prerequisites:
                proc_args, kwargs = proc_def.get_args_kwargs()
                subprocess.run(proc_args, **kwargs, check=True)
        logfilename = os.path.join(LOGS_DIR, f"{service}_{datetime.datetime.now().timestamp()}.txt")
        logfilename_sym = os.path.join(LOGS_DIR, f"{service}.txt")
        logfile = open(logfilename, "w")
        try:
            os.remove(logfilename_sym)
        except FileNotFoundError:
            pass
        os.symlink(logfilename, logfilename_sym)
        eprint(f"Launching main processes for {service}")
        pids = []
        try:
            for proc_def in managed_proc.mains:
                proc_args, kwargs = proc_def.get_args_kwargs()
                pids.append(
                    subprocess.Popen(
                        proc_args,
                        **kwargs,
                        # Start in a new session so that the process stays alive after
                        # the python program ends.
                        start_new_session=True,
                        # To stop the new process from reading out of the existing
                        # terminal's STDIN.
                        stdin=subprocess.DEVNULL,
                        stdout=logfile,
                        stderr=logfile,
                    ).pid
                )
            proc_info = dict(pids=pids, logfile=logfilename)
            with open(PROC_INFO_FULLPATH, "w") as f:
                json.dump(proc_info, f)
        except Exception as e:
            for pid in pids:
                _cleanup_pid(pid)
            raise
        eprint(f"Started {service}. Saved process info to {PROC_INFO_FULLPATH}: {proc_info}")

    # If wait is enabled, run healthcheck after starting all services
    if args.wait:
        eprint("Waiting for services to be healthy...")
        _run_healthcheck(services, args.wait_timeout)


def run_stop(args):
    for service in _get_services(args):
        # Check if there is an existing proc entry for the service.
        PROC_INFO_FULLPATH = os.path.abspath(os.path.join(PROC_INFO_DIR, service))
        try:
            with open(PROC_INFO_FULLPATH) as f:
                proc_info = json.load(f)
        except FileNotFoundError:
            eprint(f"No existing process info found for {service} in {PROC_INFO_FULLPATH}. Skipping")
            continue

        for pid in proc_info["pids"]:
            _cleanup_pid(pid)
        try:
            os.remove(PROC_INFO_FULLPATH)
        except FileNotFoundError:
            pass
        eprint(f"Stopped {service}. Removed process info file {PROC_INFO_FULLPATH} with contents: {proc_info}")


def run_restart(args):
    run_stop(args)
    run_start(args)


def run_info(args):
    for service in _get_services(args):
        PROC_INFO_FULLPATH = os.path.abspath(os.path.join(PROC_INFO_DIR, service))
        try:
            with open(PROC_INFO_FULLPATH) as f:
                proc_info = json.load(f)
        except FileNotFoundError:
            eprint(f"No existing process info found for {service} in {PROC_INFO_FULLPATH}")
            continue
        running_pids = [pid for pid in proc_info["pids"] if _is_pid_running(pid)]
        proc_info_str = f": {proc_info}"
        if len(running_pids) == len(proc_info["pids"]):
            eprint(f"All processes in {service} are running: {proc_info}")
        elif len(running_pids) == 0:
            eprint(f"No processes in {service} are running: {proc_info}")
        else:
            eprint(
                f"{service} is partially running. You may want to stop/restart this service. Running {running_pids} from {proc_info}"
            )


def run_logfile(args):
    service = args.service
    assert service in SERVICES
    PROC_INFO_FULLPATH = os.path.abspath(os.path.join(PROC_INFO_DIR, service))
    try:
        with open(PROC_INFO_FULLPATH) as f:
            proc_info = json.load(f)
    except FileNotFoundError:
        raise Exception(f"No existing process info found for {service} in {PROC_INFO_FULLPATH}")
    if not any(_is_pid_running(pid) for pid in proc_info["pids"]):
        raise Exception(f"No processes are running for {service} at {PROC_INFO_FULLPATH}: {proc_info}")
    print(proc_info["logfile"])


def run_tail(args):
    services = args.include if args.include else _get_services(args)
    if args.exclude:
        services = [s for s in services if s not in args.exclude]

    # Find the longest service name to determine padding
    max_service_length = max(len(service) for service in services)

    # Create a color map for services
    color_map = {service: SERVICE_COLORS[i % len(SERVICE_COLORS)] for i, service in enumerate(services)}

    # Start tail processes for each service
    processes = []
    for service in services:
        assert service in SERVICES
        tail_args = list(args.tail_args) if args.tail_args else []
        tail_args = [t for t in tail_args if t != "--"]
        if not any(arg in ("-f", "-F") for arg in tail_args):
            tail_args = ["-F"] + tail_args

        if args.no_prefix:
            # Just run tail without any prefix
            cmd = ["tail"] + tail_args + [os.path.join(LOGS_DIR, f"{service}.txt")]
            processes.append(subprocess.Popen(cmd))
        else:
            # Use awk to prefix each line with the service name, padded to max length
            color_start = "" if args.no_color else color_map[service]
            color_end = "" if args.no_color else RESET_COLOR
            padded_service = service.rjust(max_service_length)
            awk_cmd = f"awk '{{print \"{color_start}{padded_service}:{color_end} \" $0}}'"
            tail_cmd = ["tail"] + tail_args + [os.path.join(LOGS_DIR, f"{service}.txt")]
            tail_cmd_str = " ".join([shlex.quote(str(arg)) for arg in tail_cmd])
            cmd = f"{tail_cmd_str} | {awk_cmd}"
            processes.append(subprocess.Popen(cmd, shell=True))

    try:
        # Wait for all processes
        for p in processes:
            p.wait()
    except KeyboardInterrupt:
        # Clean up processes on Ctrl+C
        for p in processes:
            p.terminate()


def run_healthcheck(args):
    _run_healthcheck(_get_services(args), args.max_time, args.sleep_time, args.request_timeout)


def _run_healthcheck(services: list[str], max_time: float, sleep_time: float = 1, request_timeout: float = 10) -> None:
    for service in services:
        healthcheck_url = SERVICES[service].get("healthcheck_url")
        if healthcheck_url is None:
            continue
        eprint(f"Running healthcheck for service {service}")
        if not healthcheck.check_get_retry_max_time(
            healthcheck_url, max_time=max_time, sleep_time=sleep_time, request_timeout=request_timeout
        ):
            raise Exception(f"Healthcheck for service {service} failed at URL {healthcheck_url}")


if __name__ == "__main__":
    os.makedirs(PROC_INFO_DIR, exist_ok=True)
    os.makedirs(LOGS_DIR, exist_ok=True)

    parser = argparse.ArgumentParser(
        prog="Manage local braintrust services",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    subparsers = parser.add_subparsers(help="sub-command help", required=True)

    def make_subparser(name, help, include_mode_flags=False):
        subparser = subparsers.add_parser(name, help=help, formatter_class=argparse.ArgumentDefaultsHelpFormatter)
        subparser.add_argument(
            "--include",
            nargs="*",
            default=[],
            help=f"Whitelist of services to operate on, from {list(SERVICES.keys())}. May not also specify --exclude. If neither are specified, will run over all defined services",
        )
        subparser.add_argument(
            "--exclude",
            nargs="*",
            default=[],
            help=f"Blacklist of services to operate on, from {list(SERVICES.keys())}. May not also specify --include. If neither are specified, will run over all defined services",
        )

        if include_mode_flags:
            for key, val in SERVICES.items():
                all_modes = [m[0] for m in val["modes"]]
                subparser.add_argument(
                    f"--{key}-mode", choices=all_modes, default=all_modes[0], help="Mode to run service in"
                )

        return subparser

    parser_start = make_subparser("start", help="start services", include_mode_flags=True)
    parser_start.set_defaults(func=run_start)
    parser_start.add_argument("--wait", action="store_true", help="Wait for services to be healthy after starting")
    parser_start.add_argument(
        "--wait-timeout",
        type=float,
        default=30,
        help="Maximum time in seconds to wait for services to become healthy",
    )

    parser_stop = make_subparser("stop", help="stop services")
    parser_stop.set_defaults(func=run_stop)

    parser_restart = make_subparser("restart", help="restart services", include_mode_flags=True)
    parser_restart.set_defaults(func=run_restart)
    parser_restart.add_argument("--wait", action="store_true", help="Wait for services to be healthy after starting")
    parser_restart.add_argument(
        "--wait-timeout",
        type=float,
        default=30,
        help="Maximum time in seconds to wait for services to become healthy",
    )

    parser_info = make_subparser("info", help="print information about services")
    parser_info.set_defaults(func=run_info)

    parser_logfile = subparsers.add_parser(
        "logfile",
        help="Print out the logfile of the currently-active service",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser_logfile.add_argument("service", help="name of service")
    parser_logfile.set_defaults(func=run_logfile)

    parser_tail = make_subparser("tail", help="tail the logfile of a service")
    parser_tail.add_argument("--no-color", action="store_true", help="disable colored output")
    parser_tail.add_argument("--no-prefix", action="store_true", help="disable service name prefix")
    parser_tail.add_argument("tail_args", nargs=argparse.REMAINDER, help="additional arguments to pass to tail")
    parser_tail.set_defaults(func=run_tail)

    parser_healthcheck = make_subparser(
        "healthcheck",
        help="Check if all services are running by curling a URL. The command fails if any of the services is not running",
    )
    parser_healthcheck.add_argument(
        "--max-time",
        type=float,
        default=10,
        help="The amount of time to try curling before declaring a service not healthy",
    )
    parser_healthcheck.add_argument(
        "--sleep-time", type=float, default=1, help="The number of seconds to sleep in between each curl attempt"
    )
    parser_healthcheck.add_argument(
        "--request-timeout",
        type=float,
        default=120,
        help="Timeout for each individual curl request. This avoids waiting forever in case the server receives the requests but never responds.",
    )
    parser_healthcheck.set_defaults(func=run_healthcheck)

    args = parser.parse_args()

    should_take_lockfile = args.func in [run_start, run_stop, run_restart]
    if should_take_lockfile:
        BT_SERVICES_LOCKFILE_FULLPATH = os.path.join(PROC_INFO_DIR, BT_SERVICES_LOCKFILE)
        try:
            lockfile = os.open(BT_SERVICES_LOCKFILE_FULLPATH, os.O_CREAT | os.O_EXCL)
        except FileExistsError:
            eprint(
                f"Cannot run multiple instances of `bt_services` concurrently. If you are certain no other instances are running, remove the lockfile at {os.path.abspath(BT_SERVICES_LOCKFILE_FULLPATH)}"
            )
            sys.exit(1)

    try:
        args.func(args)
    finally:
        if should_take_lockfile:
            os.close(lockfile)
            os.remove(BT_SERVICES_LOCKFILE_FULLPATH)
